#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网口摄像头YOLO目标检测程序
结合ip_camera_stream.py的网口摄像头连接功能和detect_strengthen.py的YOLO检测功能
"""

import argparse
import cv2
import torch
import numpy as np
from pathlib import Path
import sys
import os
from time import time
import socket

# 添加YOLOv5路径
FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]  # YOLOv5 root directory
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))  # add ROOT to PATH
ROOT = Path(os.path.relpath(ROOT, Path.cwd()))  # relative

from models.common import DetectMultiBackend
from utils.general import (
    check_img_size,
    check_imshow,
    colorstr,
    non_max_suppression,
    scale_boxes,
)
from utils.torch_utils import select_device, smart_inference_mode
from utils.augmentations import letterbox
from ultralytics.utils.plotting import Annotator, colors


class IPCameraYOLODetector:
    """
    网口摄像头YOLO目标检测类
    """

    def __init__(self,
                 camera_url="rtsp://************:554/stream1",
                 weights=ROOT / "weights/yolov5s.pt",
                 data=ROOT / "data/coco128.yaml",
                 device="",
                 conf_thres=0.35,
                 iou_thres=0.45,
                 imgsz=640,
                 line_thickness=3,
                 view_img=True,
                 save_results=True,
                 project=ROOT / "runs/detect",
                 name="ip_camera_exp",
                 augment=False):
        """
        初始化检测器

        Args:
            camera_url: 网口摄像头URL
            weights: YOLO模型权重文件路径
            data: 数据集yaml文件路径
            device: 设备选择 (cuda/cpu)
            conf_thres: 置信度阈值
            iou_thres: NMS IoU阈值
            imgsz: 推理图像尺寸
            line_thickness: 检测框线条粗细
            view_img: 是否显示检测结果
            save_results: 是否保存检测结果
            project: 保存结果的项目目录
            name: 实验名称
            augment: 是否启用图像增强
        """
        self.camera_url = camera_url
        self.conf_thres = conf_thres
        self.iou_thres = iou_thres
        self.imgsz = imgsz
        self.line_thickness = line_thickness
        self.view_img = view_img
        self.save_results = save_results
        self.augment = augment
        
        # 设备选择
        self.device = select_device(device)
        print(f'使用设备: {self.device}')
        
        # 加载YOLO模型
        self.model = DetectMultiBackend(weights, device=self.device, dnn=False, data=data, fp16=False)
        self.stride, self.names, self.pt = self.model.stride, self.model.names, self.model.pt
        self.imgsz = check_img_size(imgsz, s=self.stride)
        
        # 初始化摄像头
        self.cap = self.initialize_camera()
        
        # 设置保存目录
        if save_results:
            from utils.general import increment_path
            self.save_dir = increment_path(Path(project) / name, exist_ok=False)
            self.save_dir.mkdir(parents=True, exist_ok=True)
            print(f"检测结果将保存到: {self.save_dir}")
        
        # 模型预热
        self.model.warmup(imgsz=(1, 3, self.imgsz, self.imgsz))
        
        # 跌倒检测计数器
        self.fall_count = 0
        self.fall_warning_sent = False
        
    def initialize_camera(self):
        """
        初始化并测试IP摄像头连接
        """
        print(f"正在连接摄像头: {self.camera_url}")
        cap = cv2.VideoCapture(self.camera_url)
        
        # 设置缓冲区大小以减少延迟
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        
        # 测试连接
        if not cap.isOpened():
            print(f"错误: 无法连接到摄像头 {self.camera_url}")
            print("请检查:")
            print(f"1. 摄像头IP地址是否正确 (当前: {self.camera_url})")
            print("2. 摄像头是否已开启并正常工作")
            print("3. 网络连接是否稳定")
            print("4. RTSP流地址是否正确")
            print("\n常见的摄像头URL格式:")
            print("- rtsp://用户名:密码@IP地址:554/stream1")
            print("- rtsp://IP地址:554/stream1")
            print("- rtsp://IP地址/live/ch1")
            raise ConnectionError(f"无法连接到摄像头: {self.camera_url}")
        
        print(f"成功连接到摄像头 {self.camera_url}")
        return cap
    
    def send_udp_warning(self, message="light on", host="***************", port=8080):
        """
        发送UDP警告信息
        """
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.sendto(message.encode(), (host, port))
            sock.close()
            print(f"UDP警告已发送到 {host}:{port} - {message}")
        except Exception as e:
            print(f"发送UDP警告失败: {e}")
    
    def preprocess_frame(self, frame):
        """
        预处理帧数据，使用letterbox进行图像增强
        """
        # 使用letterbox进行图像预处理（保持宽高比，填充）
        img = letterbox(frame, new_shape=(self.imgsz, self.imgsz), stride=self.stride, auto=False)[0]

        # 转换颜色空间 BGR -> RGB
        img = img[:, :, ::-1].transpose(2, 0, 1)  # BGR to RGB, HWC to CHW

        # 确保数组连续性
        img = np.ascontiguousarray(img)

        # 转换为tensor并归一化
        img = torch.from_numpy(img).to(self.device)
        img = img.float() / 255.0  # 归一化到0-1

        # 添加batch维度
        if len(img.shape) == 3:
            img = img.unsqueeze(0)

        return img
    
    @smart_inference_mode()
    def detect_frame(self, frame):
        """
        对单帧进行YOLO检测
        """
        try:
            # 预处理
            img = self.preprocess_frame(frame)

            # 推理（使用augment参数）
            pred = self.model(img, augment=self.augment, visualize=False)

            # NMS
            pred = non_max_suppression(pred, self.conf_thres, self.iou_thres, classes=None, agnostic=False, max_det=1000)

            return pred[0] if len(pred) > 0 else torch.empty((0, 6))  # 返回第一个batch的结果，防止空结果错误

        except Exception as e:
            print(f"检测帧时发生错误: {e}")
            return torch.empty((0, 6))  # 返回空的检测结果
    
    def draw_detections(self, frame, detections):
        """
        在帧上绘制检测结果
        """
        try:
            annotator = Annotator(frame, line_width=self.line_thickness, example=str(self.names))

            if len(detections) > 0:
                # 将检测框从模型尺寸缩放到原始帧尺寸
                detections = detections.clone()  # 创建副本以避免inplace操作错误
                detections[:, :4] = scale_boxes((self.imgsz, self.imgsz), detections[:, :4], frame.shape).round()

                # 统计各类别检测数量
                fall_detected_this_frame = False
                for c in detections[:, 5].unique():
                    n = (detections[:, 5] == c).sum()
                    class_name = self.names[int(c)]
                    print(f"检测到 {n} 个 {class_name}")

                    # 检查是否检测到跌倒
                    if class_name == 'fall':
                        fall_detected_this_frame = True

                # 跌倒检测逻辑
                if fall_detected_this_frame:
                    self.fall_count += 1
                    if self.fall_count >= 100 and not self.fall_warning_sent:
                        self.send_udp_warning()
                        self.fall_warning_sent = True
                        print("连续检测到100帧跌倒，已发送警告！")
                else:
                    # 如果没有检测到跌倒，重置计数器
                    self.fall_count = 0
                    self.fall_warning_sent = False

                # 绘制检测框
                for *xyxy, conf, cls in reversed(detections):
                    c = int(cls)
                    label = f"{self.names[c]} {conf:.2f}"
                    annotator.box_label(xyxy, label, color=colors(c, True))

            return annotator.result()

        except Exception as e:
            print(f"绘制检测结果时发生错误: {e}")
            return frame  # 返回原始帧
    
    def run_detection(self):
        """
        运行实时检测
        """
        print("开始实时检测，按 'q' 键退出...")

        frame_count = 0
        start_time = time()
        fps_start_time = start_time
        current_fps = 0

        try:
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    print("无法获取帧，尝试重新连接...")
                    self.cap.release()
                    self.cap = self.initialize_camera()
                    continue

                frame_count += 1

                # 进行检测
                detections = self.detect_frame(frame)

                # 绘制检测结果
                result_frame = self.draw_detections(frame, detections)

                # 计算并显示FPS（防止除零错误）
                if frame_count % 30 == 0:  # 每30帧计算一次FPS
                    end_time = time()
                    time_diff = end_time - fps_start_time
                    if time_diff > 0:  # 防止除零错误
                        current_fps = 30 / time_diff
                        fps_start_time = end_time
                        print(f"FPS: {current_fps:.1f}")

                # 在画面上显示FPS（防止除零错误）
                cv2.putText(result_frame, f'FPS: {int(current_fps)}',
                           (20, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                # 显示检测结果
                if self.view_img:
                    cv2.imshow('网口摄像头YOLO检测', result_frame)
                    
                    # 按q键退出
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                
                # 保存检测结果（可选）
                if self.save_results and frame_count % 100 == 0:  # 每100帧保存一次
                    save_path = self.save_dir / f"frame_{frame_count:06d}.jpg"
                    cv2.imwrite(str(save_path), result_frame)
                    
        except KeyboardInterrupt:
            print("\n检测被用户中断")
        except Exception as e:
            print(f"检测过程中发生错误: {e}")
        finally:
            # 释放资源
            self.cap.release()
            cv2.destroyAllWindows()
            print("检测结束，资源已释放")


def parse_arguments():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description='网口摄像头YOLO目标检测')
    parser.add_argument('--camera-url', type=str, default='rtsp://************:554/stream1',
                       help='网口摄像头URL')
    parser.add_argument('--weights', type=str, default=ROOT / 'weights/road_best.pt',
                       help='YOLO模型权重文件路径')
    parser.add_argument('--data', type=str, default=ROOT / 'data/data_road.yaml',
                       help='数据集yaml文件路径')
    parser.add_argument('--device', default='', help='设备选择: cuda device, i.e. 0 or 0,1,2,3 or cpu')
    parser.add_argument('--conf-thres', type=float, default=0.35, help='置信度阈值')
    parser.add_argument('--iou-thres', type=float, default=0.45, help='NMS IoU阈值')
    parser.add_argument('--imgsz', type=int, default=640, help='推理图像尺寸')
    parser.add_argument('--line-thickness', type=int, default=5, help='检测框线条粗细')
    parser.add_argument('--view-img', action='store_true', default=True, help='显示检测结果')
    parser.add_argument('--save-results', action='store_true', help='保存检测结果')
    parser.add_argument('--augment', action='store_true', help='启用图像增强推理')
    parser.add_argument('--project', default=ROOT / 'runs/detect', help='保存结果的项目目录')
    parser.add_argument('--name', default='ip_camera_exp', help='实验名称')

    return parser.parse_args()


if __name__ == "__main__":
    args = parse_arguments()
    
    # 创建检测器
    detector = IPCameraYOLODetector(
        camera_url=args.camera_url,
        weights=args.weights,
        data=args.data,
        device=args.device,
        conf_thres=args.conf_thres,
        iou_thres=args.iou_thres,
        imgsz=args.imgsz,
        line_thickness=args.line_thickness,
        view_img=args.view_img,
        save_results=args.save_results,
        project=args.project,
        name=args.name,
        augment=args.augment
    )
    
    # 开始检测
    detector.run_detection()
