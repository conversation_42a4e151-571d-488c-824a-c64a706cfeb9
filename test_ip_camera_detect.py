#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试网口摄像头YOLO检测程序
使用本地摄像头或测试图片来验证检测功能
"""

import cv2
import argparse
from pathlib import Path
import sys
import os

# 添加YOLOv5路径
FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]  # YOLOv5 root directory
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))  # add ROOT to PATH

from ip_camera_yolo_detect import IPCameraYOLODetector


class TestIPCameraDetector(IPCameraYOLODetector):
    """
    测试版本的IP摄像头检测器，可以使用本地摄像头或测试图片
    """
    
    def __init__(self, test_mode="webcam", test_image=None, **kwargs):
        """
        初始化测试检测器
        
        Args:
            test_mode: 测试模式 ("webcam" 或 "image")
            test_image: 测试图片路径（当test_mode="image"时使用）
            **kwargs: 其他参数传递给父类
        """
        self.test_mode = test_mode
        self.test_image = test_image
        
        # 临时设置一个假的camera_url，避免父类初始化时连接摄像头
        original_camera_url = kwargs.get('camera_url', 'rtsp://test:554/stream1')
        kwargs['camera_url'] = 'test_mode'
        
        # 初始化其他组件（除了摄像头）
        self.conf_thres = kwargs.get('conf_thres', 0.35)
        self.iou_thres = kwargs.get('iou_thres', 0.45)
        self.imgsz = kwargs.get('imgsz', 640)
        self.line_thickness = kwargs.get('line_thickness', 3)
        self.view_img = kwargs.get('view_img', True)
        self.save_results = kwargs.get('save_results', False)
        
        # 设备选择
        from utils.torch_utils import select_device
        device = kwargs.get('device', '')
        self.device = select_device(device)
        print(f'使用设备: {self.device}')
        
        # 加载YOLO模型
        from models.common import DetectMultiBackend
        from utils.general import check_img_size
        
        weights = kwargs.get('weights', ROOT / "weights/road_best.pt")
        data = kwargs.get('data', ROOT / "data/data_road.yaml")
        
        self.model = DetectMultiBackend(weights, device=self.device, dnn=False, data=data, fp16=False)
        self.stride, self.names, self.pt = self.model.stride, self.model.names, self.model.pt
        self.imgsz = check_img_size(self.imgsz, s=self.stride)
        
        # 设置保存目录
        if self.save_results:
            from utils.general import increment_path
            project = kwargs.get('project', ROOT / "runs/detect")
            name = kwargs.get('name', "test_exp")
            self.save_dir = increment_path(Path(project) / name, exist_ok=False)
            self.save_dir.mkdir(parents=True, exist_ok=True)
            print(f"检测结果将保存到: {self.save_dir}")
        
        # 模型预热
        self.model.warmup(imgsz=(1, 3, self.imgsz, self.imgsz))
        
        # 跌倒检测计数器
        self.fall_count = 0
        self.fall_warning_sent = False
        
        # 初始化测试摄像头
        self.cap = self.initialize_test_camera()
        
        print(f"测试模式: {test_mode}")
        if test_mode == "image" and test_image:
            print(f"测试图片: {test_image}")
    
    def initialize_test_camera(self):
        """
        初始化测试摄像头（本地摄像头或图片）
        """
        if self.test_mode == "webcam":
            print("正在连接本地摄像头...")
            cap = cv2.VideoCapture(0)  # 使用本地摄像头
            
            if not cap.isOpened():
                print("错误: 无法连接到本地摄像头")
                print("请确保:")
                print("1. 摄像头已连接并正常工作")
                print("2. 没有其他程序正在使用摄像头")
                raise ConnectionError("无法连接到本地摄像头")
            
            print("成功连接到本地摄像头")
            return cap
            
        elif self.test_mode == "image":
            if not self.test_image or not Path(self.test_image).exists():
                print(f"错误: 测试图片不存在: {self.test_image}")
                raise FileNotFoundError(f"测试图片不存在: {self.test_image}")
            
            print(f"使用测试图片: {self.test_image}")
            return None  # 图片模式不需要VideoCapture对象
        
        else:
            raise ValueError(f"不支持的测试模式: {self.test_mode}")
    
    def run_detection(self):
        """
        运行测试检测
        """
        if self.test_mode == "webcam":
            self.run_webcam_detection()
        elif self.test_mode == "image":
            self.run_image_detection()
    
    def run_webcam_detection(self):
        """
        运行本地摄像头检测
        """
        print("开始本地摄像头检测，按 'q' 键退出...")
        
        try:
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    print("无法获取帧")
                    break
                
                # 进行检测
                detections = self.detect_frame(frame)
                
                # 绘制检测结果
                result_frame = self.draw_detections(frame, detections)
                
                # 显示检测结果
                if self.view_img:
                    cv2.imshow('本地摄像头YOLO检测测试', result_frame)
                    
                    # 按q键退出
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
                        
        except KeyboardInterrupt:
            print("\n检测被用户中断")
        except Exception as e:
            print(f"检测过程中发生错误: {e}")
        finally:
            # 释放资源
            if self.cap:
                self.cap.release()
            cv2.destroyAllWindows()
            print("检测结束，资源已释放")
    
    def run_image_detection(self):
        """
        运行图片检测
        """
        print(f"开始图片检测: {self.test_image}")
        
        try:
            # 读取图片
            frame = cv2.imread(str(self.test_image))
            if frame is None:
                print(f"无法读取图片: {self.test_image}")
                return
            
            print(f"图片尺寸: {frame.shape}")
            
            # 进行检测
            detections = self.detect_frame(frame)
            
            # 绘制检测结果
            result_frame = self.draw_detections(frame, detections)
            
            # 保存结果
            if self.save_results:
                save_path = self.save_dir / f"result_{Path(self.test_image).name}"
                cv2.imwrite(str(save_path), result_frame)
                print(f"检测结果已保存到: {save_path}")
            
            # 显示检测结果
            if self.view_img:
                cv2.imshow('图片YOLO检测测试', result_frame)
                print("按任意键关闭窗口...")
                cv2.waitKey(0)
                cv2.destroyAllWindows()
                
        except Exception as e:
            print(f"图片检测过程中发生错误: {e}")


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='测试网口摄像头YOLO检测程序')
    parser.add_argument('--mode', choices=['webcam', 'image'], default='webcam',
                       help='测试模式: webcam(本地摄像头) 或 image(测试图片)')
    parser.add_argument('--image', type=str, help='测试图片路径（当mode=image时使用）')
    parser.add_argument('--weights', type=str, default=ROOT / 'weights/road_best.pt',
                       help='YOLO模型权重文件路径')
    parser.add_argument('--data', type=str, default=ROOT / 'data/data_road.yaml',
                       help='数据集yaml文件路径')
    parser.add_argument('--device', default='', help='设备选择: cuda device, i.e. 0 or 0,1,2,3 or cpu')
    parser.add_argument('--conf-thres', type=float, default=0.35, help='置信度阈值')
    parser.add_argument('--iou-thres', type=float, default=0.45, help='NMS IoU阈值')
    parser.add_argument('--imgsz', type=int, default=640, help='推理图像尺寸')
    parser.add_argument('--line-thickness', type=int, default=5, help='检测框线条粗细')
    parser.add_argument('--save-results', action='store_true', help='保存检测结果')
    parser.add_argument('--project', default=ROOT / 'runs/detect', help='保存结果的项目目录')
    parser.add_argument('--name', default='test_exp', help='实验名称')
    
    args = parser.parse_args()
    
    # 检查参数
    if args.mode == 'image' and not args.image:
        print("错误: 使用image模式时必须指定--image参数")
        return
    
    try:
        # 创建测试检测器
        detector = TestIPCameraDetector(
            test_mode=args.mode,
            test_image=args.image,
            weights=args.weights,
            data=args.data,
            device=args.device,
            conf_thres=args.conf_thres,
            iou_thres=args.iou_thres,
            imgsz=args.imgsz,
            line_thickness=args.line_thickness,
            view_img=True,
            save_results=args.save_results,
            project=args.project,
            name=args.name
        )
        
        # 开始检测
        detector.run_detection()
        
    except Exception as e:
        print(f"程序运行失败: {e}")


if __name__ == "__main__":
    main()
