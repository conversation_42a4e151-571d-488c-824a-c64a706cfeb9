import cv2
import torch
from time import time

class ObjectDetection:
    """
    使用YOLOv5模型进行对象检测的类
    """
    def __init__(self, url="rtsp://192.168.1.88:554/stream1"):
        """
        初始化类的属性和方法
        """
        self.url = url
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f'使用设备: {self.device}')
        
        # 加载YOLOv5模型
        self.model = self.load_model()
        
        # 设置类别名称
        self.classes = self.model.names
        
        # 初始化摄像头
        self.cap = self.initialize_camera()
    
    def load_model(self):
        """
        加载预训练的YOLOv5模型
        """
        model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
        return model
    
    def initialize_camera(self):
        """
        初始化并测试IP摄像头连接
        """
        cap = cv2.VideoCapture(self.url)
        
        # 测试连接
        if not cap.isOpened():
            print(f"错误: 无法连接到摄像头 {self.url}")
            print("请检查:")
            print("1. 摄像头IP地址是否正确 (当前: {self.url})")
            print("2. 摄像头是否已开启并正常工作")
            print("3. 网络连接是否稳定")
            exit()
        
        print(f"成功连接到摄像头 {self.url}")
        return cap
    
    def get_video_capture(self):
        """
        返回视频捕获对象
        """
        return self.cap
    
    def score_frame(self, frame):
        """
        使用YOLOv5模型对一帧图像进行检测
        """
        self.model.to(self.device)
        frame = [frame]
        results = self.model(frame)
        labels, cord = results.xyxyn[0][:, -1], results.xyxyn[0][:, :-1]
        return labels, cord
    
    def class_to_label(self, x):
        """
        将类别索引转换为类别名称
        """
        return self.classes[int(x)]
    
    def plot_boxes(self, results, frame):
        """
        在图像上绘制检测框和标签
        """
        labels, cord = results
        n = len(labels)
        x_shape, y_shape = frame.shape[1], frame.shape[0]
        
        for i in range(n):
            row = cord[i]
            if row[4] >= 0.3:  # 置信度阈值
                x1, y1, x2, y2 = int(row[0]*x_shape), int(row[1]*y_shape), int(row[2]*x_shape), int(row[3]*y_shape)
                bgr = (0, 255, 0)  # 框的颜色 (绿色)
                cv2.rectangle(frame, (x1, y1), (x2, y2), bgr, 2)
                cv2.putText(frame, self.class_to_label(labels[i]), (x1, y1-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.9, bgr, 2)
        
        return frame
    
    def detect(self):
        """
        执行实时对象检测
        """
        while True:
            ret, frame = self.cap.read()
            if not ret:
                print("无法获取帧，退出...")
                break
            
            start_time = time()
            results = self.score_frame(frame)
            frame = self.plot_boxes(results, frame)
            
            end_time = time()
            fps = 1 / (end_time - start_time)
            
            # 在画面上显示FPS
            cv2.putText(frame, f'FPS: {int(fps)}', (20, 70), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 255, 0), 2)
            
            # 显示结果
            cv2.imshow('YOLOv5对象检测', frame)
            
            # 按q键退出
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        # 释放资源
        self.cap.release()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    # 创建对象并开始检测
    detector = ObjectDetection()
    detector.detect()