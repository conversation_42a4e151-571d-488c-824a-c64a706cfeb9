# 网口摄像头YOLO检测程序使用说明

## 🎯 程序功能

本程序提供了完整的网口摄像头YOLO目标检测解决方案，包含以下功能：

### ✅ 已修复的问题
- **除零错误**：修复了FPS计算中的除零错误
- **图像增强**：添加了letterbox图像预处理和增强功能
- **错误处理**：完善的异常处理机制
- **推理模式**：修复了PyTorch推理模式的inplace操作问题

### 🚀 主要特性
1. **网口摄像头支持**：支持RTSP/RTMP/HTTP流
2. **图像增强**：使用letterbox保持宽高比的图像预处理
3. **实时检测**：GPU加速的实时目标检测
4. **可调检测框**：线条粗细可自定义
5. **结果保存**：检测结果保存到runs/detect文件夹
6. **错误诊断**：详细的连接错误诊断信息
7. **小窗口显示**：800x600像素的紧凑显示窗口

## 📁 文件说明

- `ip_camera_yolo_detect.py` - 主程序，用于网口摄像头检测
- `test_ip_camera_detect.py` - 测试程序，支持本地摄像头和图片测试
- `IP_CAMERA_YOLO_使用说明.md` - 本说明文档

## 🔧 安装要求

确保已安装以下依赖：
```bash
pip install torch torchvision opencv-python numpy
```

## 📋 使用方法

### 1. 网口摄像头检测（主程序）

#### 基本使用：
```bash
python ip_camera_yolo_detect.py --camera-url "rtsp://您的摄像头IP:554/stream1"
```

#### 完整参数示例：
```bash
python ip_camera_yolo_detect.py \
    --camera-url "rtsp://*************:554/stream1" \
    --weights "weights/road_best.pt" \
    --data "data/data_road.yaml" \
    --conf-thres 0.4 \
    --iou-thres 0.45 \
    --imgsz 640 \
    --line-thickness 8 \
    --augment \
    --save-results \
    --view-img
```

#### 道路检测：
```bash
python ip_camera_yolo_detect.py \
    --camera-url "rtsp://*************:554/stream1" \
    --weights "weights/road_best.pt" \
    --line-thickness 6
```

### 2. 测试程序

#### 图片测试：
```bash
python test_ip_camera_detect.py \
    --mode image \
    --image "detect_stuff/00026.jpg" \
    --save-results \
    --line-thickness 8
```

#### 本地摄像头测试：
```bash
python test_ip_camera_detect.py \
    --mode webcam \
    --line-thickness 6 \
    --weights "weights/road_best.pt"
```

## 🔧 参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--camera-url` | 网口摄像头URL | `rtsp://************:554/stream1` |
| `--weights` | YOLO模型权重文件路径 | `weights/road_best.pt` |
| `--data` | 数据集yaml文件路径 | `data/data_road.yaml` |
| `--device` | 设备选择 (cuda/cpu) | 自动选择 |
| `--conf-thres` | 置信度阈值 | 0.35 |
| `--iou-thres` | NMS IoU阈值 | 0.45 |
| `--imgsz` | 推理图像尺寸 | 640 |
| `--line-thickness` | 检测框线条粗细 | 5 |
| `--augment` | 启用图像增强推理 | False |
| `--view-img` | 显示检测结果 | True |
| `--save-results` | 保存检测结果 | False |
| `--project` | 保存结果的项目目录 | `runs/detect` |
| `--name` | 实验名称 | `ip_camera_exp` |

## 🌐 网口摄像头URL格式

常见的网口摄像头URL格式：

### RTSP协议：
- `rtsp://用户名:密码@IP地址:554/stream1`
- `rtsp://IP地址:554/stream1`
- `rtsp://IP地址/live/ch1`
- `rtsp://IP地址:8554/stream`

### HTTP协议：
- `http://IP地址:8080/video`
- `http://用户名:密码@IP地址/mjpeg`

### 示例：
```bash
# 海康威视摄像头
rtsp://admin:password@*************:554/Streaming/Channels/101

# 大华摄像头
rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0

# 通用RTSP流
rtsp://*************:554/stream1
```

## 🎯 检测模型

程序支持多种检测模型：

| 权重文件 | 检测类别 | 用途 |
|----------|----------|------|
| `road_best.pt` | 道路缺陷检测 | 裂缝、井盖、坑洞等 |
| `best_diedao.pt` | 跌倒检测 | 人员跌倒检测 |
| `best_accident.pt` | 事故检测 | 交通事故检测 |
| `best_color.pt` | 颜色检测 | 颜色分类检测 |

## 🖼️ 显示窗口

程序使用紧凑的显示窗口：
- 窗口大小：800x600像素
- 可调整大小的窗口
- 按'q'键退出检测

## 🐛 故障排除

### 1. 摄像头连接失败
```
错误: 无法连接到摄像头 rtsp://...
```
**解决方案：**
- 检查摄像头IP地址是否正确
- 确认摄像头已开启并正常工作
- 检查网络连接是否稳定
- 验证RTSP流地址格式是否正确

### 2. 除零错误
```
float division by zero
```
**解决方案：**
- 已在新版本中修复
- 使用最新的程序版本

### 3. 推理模式错误
```
Inplace update to inference tensor outside InferenceMode
```
**解决方案：**
- 已在新版本中修复
- 使用tensor.clone()避免inplace操作

### 4. 模型加载失败
```
模型加载失败
```
**解决方案：**
- 检查权重文件路径是否正确
- 确认data.yaml文件存在
- 验证CUDA环境是否正常

## 📊 性能优化

### 1. 提高检测速度：
- 使用较小的图像尺寸：`--imgsz 416`
- 关闭图像增强：不使用`--augment`
- 使用GPU：确保CUDA可用

### 2. 提高检测精度：
- 使用较大的图像尺寸：`--imgsz 1280`
- 启用图像增强：`--augment`
- 降低置信度阈值：`--conf-thres 0.25`

### 3. 减少误检：
- 提高置信度阈值：`--conf-thres 0.5`
- 调整IoU阈值：`--iou-thres 0.5`

## 📝 更新日志

### v2.1 (当前版本)
- ✅ 删除跌倒检测相关代码
- ✅ 调小显示窗口(800x600)
- ✅ 简化程序逻辑
- ✅ 优化用户体验

### v2.0
- ✅ 修复除零错误
- ✅ 添加图像增强功能
- ✅ 完善错误处理
- ✅ 优化FPS计算
- ✅ 添加letterbox预处理

### v1.0
- 基础网口摄像头检测功能
- 可调检测框粗细

## 📞 技术支持

如果遇到问题，请检查：
1. 网络连接是否正常
2. 摄像头URL格式是否正确
3. 权重文件是否存在
4. Python环境是否完整

程序已经过充分测试，支持多种摄像头品牌和协议。
